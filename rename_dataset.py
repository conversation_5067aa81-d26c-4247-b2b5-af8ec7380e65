import os
from pathlib import Path
import re

# === CONFIGURATION ===
REMOVE_TEXT = "*_*_"                # Wildcard à supprimer dans le nom
ADD_SUFFIX = ""                     # Suffixe à ajouter (avant l'extension)
ADD_PREFIX = ""                     # Préfixe à ajouter
ADD_SERIAL_SUFFIX = False           # Ajouter une sérialisation en suffixe ? (ex: _0001)
ADD_SERIAL_PREFIX = False           # Ajouter une sérialisation en préfixe ? (ex: 0001_)
SERIAL_REPLACE_ALL = False          # Remplacer complètement le nom par la sérialisation ?

# === Dossier cible ===
FOLDER_PATH = r"C:\Users\<USER>\Documents\4Corrosion\example_folder"

def wildcard_to_regex_fragment(wildcard):
    """
    Convertit un motif wildcard type '*_*_' en un fragment regex.
    Par exemple, '*_*_' devient '.*?_.*?_'
    """
    # Remplacer chaque * par .*? pour correspondance non-gourmande
    return wildcard.replace("*", ".*?")

def remove_wildcard_fragment_from_stem(stem, pattern):
    """
    Supprime la première correspondance du motif wildcard dans le nom.
    """
    regex_fragment = wildcard_to_regex_fragment(pattern)
    match = re.search(regex_fragment, stem)
    if match:
        return stem.replace(match.group(0), "", 1)  # Retire seulement la première occurrence
    return stem

def rename_files(folder_path):
    folder = Path(folder_path)
    files = sorted([f for f in folder.iterdir() if f.is_file()])

    for idx, file in enumerate(files, 1):
        stem = file.stem
        ext = file.suffix
        serial = f"{idx:04d}"

        if SERIAL_REPLACE_ALL:
            new_stem = serial
        else:
            # Supprimer le motif wildcard partiel
            if REMOVE_TEXT:
                stem = remove_wildcard_fragment_from_stem(stem, REMOVE_TEXT)

            new_stem = stem

            # Ajouter préfixe/suffixe
            if ADD_PREFIX:
                new_stem = ADD_PREFIX + new_stem
            if ADD_SUFFIX:
                new_stem = new_stem + ADD_SUFFIX

            # Sérialisation optionnelle
            if ADD_SERIAL_PREFIX:
                new_stem = f"{serial}_{new_stem}"
            if ADD_SERIAL_SUFFIX:
                new_stem = f"{new_stem}_{serial}"

        new_name = f"{new_stem}{ext}"
        new_path = folder / new_name
        os.rename(file, new_path)

    print(f"✅ {len(files)} fichiers renommés dans : {folder_path}")

# === Lancer ===
if __name__ == "__main__":
    rename_files(FOLDER_PATH)
